using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using DriverManagementSystem.Models;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة النماذج الاحترافية
    /// </summary>
    public class ProfessionalTemplatesService
    {
        private readonly IDataService _dataService;
        
        public ProfessionalTemplatesService()
        {
            _dataService = new SqliteDataService();
        }

        /// <summary>
        /// تفعيل جميع النماذج الاحترافية
        /// </summary>
        public async Task<bool> ActivateAllProfessionalTemplatesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🎨 بدء تفعيل النماذج الاحترافية...");

                // تفعيل قوالب الرسائل
                var messageTemplatesActivated = await ActivateMessageTemplatesAsync();
                
                // تفعيل نماذج المستخدمين
                var userTemplatesActivated = await ActivateUserTemplatesAsync();
                
                // تفعيل قوالب التقارير
                var reportTemplatesActivated = await ActivateReportTemplatesAsync();

                var allActivated = messageTemplatesActivated && userTemplatesActivated && reportTemplatesActivated;
                
                if (allActivated)
                {
                    // حفظ حالة التفعيل
                    await SaveActivationStatusAsync();
                    System.Diagnostics.Debug.WriteLine("✅ تم تفعيل جميع النماذج الاحترافية بنجاح!");
                }
                
                return allActivated;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفعيل النماذج الاحترافية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تفعيل قوالب الرسائل الاحترافية
        /// </summary>
        private async Task<bool> ActivateMessageTemplatesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📱 تفعيل قوالب الرسائل الاحترافية...");
                
                var templates = GetProfessionalMessageTemplates();
                
                // هنا يمكن حفظ القوالب في قاعدة البيانات إذا لزم الأمر
                // await _dataService.SaveMessageTemplatesAsync(templates);
                
                System.Diagnostics.Debug.WriteLine($"✅ تم تفعيل {templates.Count} قالب رسالة احترافي");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفعيل قوالب الرسائل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تفعيل نماذج المستخدمين الاحترافية
        /// </summary>
        private async Task<bool> ActivateUserTemplatesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("👥 تفعيل نماذج المستخدمين الاحترافية...");
                
                // التحقق من وجود المستخدم الافتراضي
                var context = new Data.ApplicationDbContext();
                await context.InitializeDatabaseAsync();
                
                var userService = new UserService(context);
                var existingUsers = await userService.GetAllUsersAsync();
                
                if (existingUsers.Count == 0)
                {
                    var adminUser = new User
                    {
                        Username = "admin",
                        FullName = "المسئول الرئيسي",
                        Email = "<EMAIL>",
                        Role = "Admin",
                        IsActive = true,
                        CreatedDate = DateTime.Now,
                        CreatedBy = "System"
                    };

                    await userService.CreateUserAsync(adminUser, "123456");
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء المستخدم الافتراضي: admin/123456");
                }
                
                System.Diagnostics.Debug.WriteLine("✅ تم تفعيل نماذج المستخدمين الاحترافية");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفعيل نماذج المستخدمين: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تفعيل قوالب التقارير الاحترافية
        /// </summary>
        private async Task<bool> ActivateReportTemplatesAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📊 تفعيل قوالب التقارير الاحترافية...");
                
                var reportTemplates = GetProfessionalReportTemplates();
                
                System.Diagnostics.Debug.WriteLine($"✅ تم تفعيل {reportTemplates.Count} قالب تقرير احترافي");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفعيل قوالب التقارير: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على قوالب الرسائل الاحترافية
        /// </summary>
        private List<MessageTemplate> GetProfessionalMessageTemplates()
        {
            return new List<MessageTemplate>
            {
                new MessageTemplate
                {
                    Name = "قالب الزيارة الميدانية الاحترافي",
                    Template = @"الأخ/{DriverName} المحترم،
يرجى تقديم عرض سعركم خلال 24 ساعة وذلك للسفر لمدة ({DaysCount} يوم) مع الأخ: {Visitors}
في المناطق التالية:
{Itinerary}
📅 تاريخ النزول: {StartDate} - 📅 تاريخ العودة: {EndDate}
وشكراً - ادارة حركة السائقين بالصندوق الاجتماعي فرع ذمار"
                },
                new MessageTemplate
                {
                    Name = "قالب مختصر احترافي",
                    Template = @"الأخ/{DriverName} المحترم،
مطلوب عرض سعر للسفر ({DaysCount} يوم) مع {Visitors}
التاريخ: {StartDate} - {EndDate}
وشكراً"
                },
                new MessageTemplate
                {
                    Name = "قالب تذكير احترافي",
                    Template = @"الأخ/{DriverName} المحترم،
تذكير بطلب عرض السعر للزيارة الميدانية
المدة: {DaysCount} يوم
يرجى الرد في أقرب وقت
شكراً"
                }
            };
        }

        /// <summary>
        /// الحصول على قوالب التقارير الاحترافية
        /// </summary>
        private List<string> GetProfessionalReportTemplates()
        {
            return new List<string>
            {
                "محضر الزيارة الميدانية الاحترافي",
                "عقد السائق المحسن",
                "تقرير عروض الأسعار التفصيلي",
                "تقرير إحصائي شامل",
                "تقرير الأداء الشهري",
                "تقرير المتابعة"
            };
        }

        /// <summary>
        /// حفظ حالة تفعيل النماذج الاحترافية
        /// </summary>
        private async Task SaveActivationStatusAsync()
        {
            try
            {
                DriverManagementSystem.Properties.Settings.Default.ProfessionalTemplatesActivated = true;
                DriverManagementSystem.Properties.Settings.Default.ActivationDate = DateTime.Now.ToString();
                DriverManagementSystem.Properties.Settings.Default.Save();
                
                System.Diagnostics.Debug.WriteLine("💾 تم حفظ حالة تفعيل النماذج الاحترافية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ حالة التفعيل: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من حالة تفعيل النماذج الاحترافية
        /// </summary>
        public bool AreProfessionalTemplatesActivated()
        {
            try
            {
                return DriverManagementSystem.Properties.Settings.Default.ProfessionalTemplatesActivated;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إعادة تعيين النماذج الاحترافية
        /// </summary>
        public async Task<bool> ResetProfessionalTemplatesAsync()
        {
            try
            {
                DriverManagementSystem.Properties.Settings.Default.ProfessionalTemplatesActivated = false;
                DriverManagementSystem.Properties.Settings.Default.ActivationDate = "";
                DriverManagementSystem.Properties.Settings.Default.Save();
                
                System.Diagnostics.Debug.WriteLine("🔄 تم إعادة تعيين النماذج الاحترافية");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة التعيين: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// نموذج قالب الرسالة
    /// </summary>
    public class MessageTemplate
    {
        public string Name { get; set; } = string.Empty;
        public string Template { get; set; } = string.Empty;
    }
}
