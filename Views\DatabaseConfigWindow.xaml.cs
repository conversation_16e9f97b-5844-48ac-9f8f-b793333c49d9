using System;
using System.Windows;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة إعدادات قاعدة البيانات
    /// </summary>
    public partial class DatabaseConfigWindow : Window
    {
        public bool ConfigurationSaved { get; private set; } = false;

        public DatabaseConfigWindow()
        {
            InitializeComponent();
            LoadCurrentSettings();
        }

        /// <summary>
        /// تحميل الإعدادات الحالية
        /// </summary>
        private void LoadCurrentSettings()
        {
            // تحديد نوع قاعدة البيانات الحالية
            if (DatabaseConfig.CurrentDatabaseType == DatabaseConfig.DatabaseType.SqlServer)
            {
                SqlServerRadio.IsChecked = true;
            }
            else
            {
                SqliteRadio.IsChecked = true;
            }

            // تحميل إعدادات SQL Server من متغيرات البيئة أو القيم الافتراضية
            ServerNameTextBox.Text = Environment.GetEnvironmentVariable("SQL_SERVER_NAME") ?? "localhost";
            DatabaseNameTextBox.Text = Environment.GetEnvironmentVariable("SQL_DATABASE_NAME") ?? "SFDSYS";
            
            var useWindowsAuth = Environment.GetEnvironmentVariable("SQL_USE_WINDOWS_AUTH") != "false";
            WindowsAuthCheckBox.IsChecked = useWindowsAuth;
            
            if (!useWindowsAuth)
            {
                UsernameTextBox.Text = Environment.GetEnvironmentVariable("SQL_USERNAME") ?? "sa";
            }

            UpdateSqlServerControlsVisibility();
        }

        /// <summary>
        /// تحديث رؤية عناصر التحكم في SQL Server
        /// </summary>
        private void UpdateSqlServerControlsVisibility()
        {
            var isSqlServer = SqlServerRadio.IsChecked == true;
            SqlServerGroup.IsEnabled = isSqlServer;
        }

        private void WindowsAuthCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            UsernameLabel.IsEnabled = false;
            UsernameTextBox.IsEnabled = false;
            PasswordLabel.IsEnabled = false;
            PasswordBox.IsEnabled = false;
        }

        private void WindowsAuthCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            UsernameLabel.IsEnabled = true;
            UsernameTextBox.IsEnabled = true;
            PasswordLabel.IsEnabled = true;
            PasswordBox.IsEnabled = true;
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            if (SqliteRadio.IsChecked == true)
            {
                MessageBox.Show("SQLite لا يحتاج اختبار اتصال", "معلومات", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            TestConnectionButton.IsEnabled = false;
            TestConnectionButton.Content = "جاري الاختبار...";

            try
            {
                // حفظ الإعدادات مؤقتاً لاختبار الاتصال
                SaveEnvironmentVariables();

                // اختبار الاتصال
                var originalDbType = DatabaseConfig.CurrentDatabaseType;
                DatabaseConfig.CurrentDatabaseType = DatabaseConfig.DatabaseType.SqlServer;

                using var context = new ApplicationDbContext();
                var canConnect = await context.Database.CanConnectAsync();

                DatabaseConfig.CurrentDatabaseType = originalDbType;

                if (canConnect)
                {
                    MessageBox.Show("تم الاتصال بنجاح!", "نجح الاختبار", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في الاتصال بقاعدة البيانات", "خطأ في الاتصال", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الاتصال:\n{ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                TestConnectionButton.IsEnabled = true;
                TestConnectionButton.Content = "اختبار الاتصال";
            }
        }

        /// <summary>
        /// حفظ متغيرات البيئة
        /// </summary>
        private void SaveEnvironmentVariables()
        {
            Environment.SetEnvironmentVariable("SQL_SERVER_NAME", ServerNameTextBox.Text);
            Environment.SetEnvironmentVariable("SQL_DATABASE_NAME", DatabaseNameTextBox.Text);
            Environment.SetEnvironmentVariable("SQL_USE_WINDOWS_AUTH", 
                WindowsAuthCheckBox.IsChecked == true ? "true" : "false");
            
            if (WindowsAuthCheckBox.IsChecked != true)
            {
                Environment.SetEnvironmentVariable("SQL_USERNAME", UsernameTextBox.Text);
                Environment.SetEnvironmentVariable("SQL_PASSWORD", PasswordBox.Password);
            }
        }

        /// <summary>
        /// حفظ وتطبيق الإعدادات
        /// </summary>
        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            SaveButton.IsEnabled = false;
            SaveButton.Content = "جاري الحفظ...";

            try
            {
                // حفظ متغيرات البيئة
                SaveEnvironmentVariables();

                // تحديد نوع قاعدة البيانات
                var newDbType = SqlServerRadio.IsChecked == true ? 
                    DatabaseConfig.DatabaseType.SqlServer : 
                    DatabaseConfig.DatabaseType.SQLite;

                // إذا تم اختيار SQL Server، قم بنقل البيانات
                if (newDbType == DatabaseConfig.DatabaseType.SqlServer)
                {
                    var migrationService = new DataMigrationService();
                    var success = await migrationService.MigrateFromSqliteToSqlServerAsync();

                    if (!success)
                    {
                        MessageBox.Show("فشل في نقل البيانات إلى SQL Server", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                // تحديث نوع قاعدة البيانات
                DatabaseConfig.CurrentDatabaseType = newDbType;

                ConfigurationSaved = true;
                MessageBox.Show("تم حفظ الإعدادات بنجاح!\nسيتم إعادة تشغيل النظام.", "نجح الحفظ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات:\n{ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveButton.IsEnabled = true;
                SaveButton.Content = "حفظ وتطبيق";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
