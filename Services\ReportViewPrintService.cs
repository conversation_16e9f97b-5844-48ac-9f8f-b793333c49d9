using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Markup;
using System.Windows.Shapes;

using DriverManagementSystem.Views;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة طباعة مبسطة لـ ReportView الأصلي
    /// </summary>
    public class ReportViewPrintService
    {
        // أبعاد A4 بوحدة DIP - محسنة لتجنب الاقتصاص
        private const double A4_WIDTH_DIP = 793.7;   // 21.0 cm
        private const double A4_HEIGHT_DIP = 1122.52; // 29.7 cm
        private const double MARGIN_DIP = 25;      // هوامش مقللة لتجنب الاقتصاص

        /// <summary>
        /// طباعة ReportView الأصلي مباشرة
        /// </summary>
        public static bool PrintReportView(ReportView reportView, string documentTitle = "تقرير الزيارة الميدانية")
        {
            try
            {
                if (reportView == null)
                {
                    MessageBox.Show("لا يوجد تقرير للطباعة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // تشخيص البيانات قبل الطباعة
                System.Diagnostics.Debug.WriteLine("🔍 بدء طباعة ReportView الأصلي...");
                if (reportView.DataContext is DriverManagementSystem.ViewModels.ReportViewModel viewModel)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ ViewModel موجود: {viewModel.GetType().Name}");
                    System.Diagnostics.Debug.WriteLine($"🔍 عدد المشاريع في ReportData: {viewModel.ReportData?.Projects?.Count ?? 0}");

                    if (viewModel.ReportData?.Projects?.Any() == true)
                    {
                        for (int i = 0; i < viewModel.ReportData.Projects.Count; i++)
                        {
                            var project = viewModel.ReportData.Projects[i];
                            System.Diagnostics.Debug.WriteLine($"📋 مشروع {i + 1}: {project.SerialNumber} - {project.ProjectNumber} - {project.ProjectName}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ لا توجد مشاريع في ReportData!");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ DataContext ليس من نوع ReportViewModel: {reportView.DataContext?.GetType().Name ?? "null"}");
                }

                // إنشاء حوار الطباعة
                var printDialog = new PrintDialog();

                // عرض حوار اختيار الطابعة
                if (printDialog.ShowDialog() == true)
                {
                    System.Diagnostics.Debug.WriteLine("🖨️ إنشاء مستند للطباعة بإعدادات الطابعة المحلية...");

                    // إنشاء مستند للطباعة من ReportView الأصلي
                    var printDocument = CreatePrintDocument(reportView);

                    if (printDocument != null)
                    {
                        // طباعة المستند بإعدادات الطابعة المحلية
                        printDialog.PrintDocument(printDocument.DocumentPaginator, documentTitle);

                        System.Diagnostics.Debug.WriteLine("✅ تم إرسال المستند للطباعة بإعدادات الطابعة المحلية");
                        MessageBox.Show("تم إرسال التقرير للطباعة بإعدادات الطابعة المحلية", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return true;
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنشاء مستند الطباعة", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }
                
                return false; // المستخدم ألغى الطباعة
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// إنشاء مستند طباعة من ReportView الأصلي مع تقسيم الصفحات عند الفواصل
        /// </summary>
        private static FixedDocument CreatePrintDocument(ReportView reportView)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ إنشاء مستند طباعة مع تقسيم الصفحات عند الفواصل");

                // التأكد من تحديث ReportView قبل الطباعة
                reportView.UpdateLayout();

                // إنشاء مستند الطباعة
                var fixedDocument = new FixedDocument();

                // البحث عن الصفحات في ReportView الأصلي
                var pages = FindPrintPages(reportView);

                System.Diagnostics.Debug.WriteLine($"📄 تم العثور على {pages.Count} صفحة للطباعة");

                // إنشاء صفحة طباعة منفصلة لكل صفحة
                for (int i = 0; i < pages.Count; i++)
                {
                    var page = pages[i];
                    System.Diagnostics.Debug.WriteLine($"🖨️ إنشاء صفحة طباعة {i + 1}");

                    // إنشاء صفحة A4
                    var fixedPage = new FixedPage
                    {
                        Width = A4_WIDTH_DIP,
                        Height = A4_HEIGHT_DIP,
                        Background = Brushes.White
                    };

                    // إنشاء VisualBrush من الصفحة الأصلية - دقة عالية وملء الصفحة بالكامل
                    var visualBrush = new VisualBrush(page)
                    {
                        Stretch = Stretch.Fill,  // ملء الصفحة بالكامل بدلاً من Uniform
                        AlignmentX = AlignmentX.Center,
                        AlignmentY = AlignmentY.Top,
                        TileMode = TileMode.None, // منع التكرار
                        ViewboxUnits = BrushMappingMode.RelativeToBoundingBox,
                        ViewportUnits = BrushMappingMode.RelativeToBoundingBox
                    };

                    // إنشاء Rectangle لعرض محتوى الصفحة - ملء الورقة بالكامل
                    var contentRectangle = new Rectangle
                    {
                        Width = A4_WIDTH_DIP - (MARGIN_DIP * 0.5), // تقليل الهوامش لملء أكبر
                        Height = A4_HEIGHT_DIP - (MARGIN_DIP * 0.5),
                        Fill = visualBrush,
                        Margin = new Thickness(MARGIN_DIP * 0.25) // هوامش أصغر
                    };

                    // إصلاح مشكلة النص المعكوس للعربية RTL
                    var transformGroup = new TransformGroup();
                    transformGroup.Children.Add(new ScaleTransform(-1, 1)); // قلب أفقي
                    transformGroup.Children.Add(new TranslateTransform(contentRectangle.Width, 0)); // تصحيح الموضع
                    contentRectangle.RenderTransform = transformGroup;

                    // إضافة المحتوى للصفحة
                    fixedPage.Children.Add(contentRectangle);

                    // إضافة رقم الصفحة في أسفل اليسار ومرفوع قليلاً
                    var pageNumberContainer = new Border
                    {
                        Background = new SolidColorBrush(Color.FromArgb(240, 255, 255, 255)), // خلفية شبه شفافة
                        BorderBrush = new SolidColorBrush(Color.FromArgb(100, 128, 128, 128)), // حدود رمادية خفيفة
                        BorderThickness = new Thickness(1, 1, 1, 1),
                        CornerRadius = new CornerRadius(3),
                        Padding = new Thickness(8, 4, 8, 4),
                        Width = 35,
                        Height = 20,
                        Child = new TextBlock
                        {
                            Text = (i + 1).ToString(), // رقم الصفحة فقط بشكل احترافي
                            FontSize = 11,
                            FontWeight = FontWeights.SemiBold,
                            Foreground = new SolidColorBrush(Color.FromRgb(64, 64, 64)), // رمادي داكن
                            TextAlignment = TextAlignment.Center,
                            HorizontalAlignment = HorizontalAlignment.Center,
                            VerticalAlignment = VerticalAlignment.Center
                        }
                    };

                    // وضع رقم الصفحة في أسفل اليسار ومرفوع قليلاً
                    Canvas.SetLeft(pageNumberContainer, 5); // تغيير إلى اليسار
                    Canvas.SetBottom(pageNumberContainer, 25); // رفعه قليلاً من 5 إلى 25

                    // إنشاء Canvas لوضع رقم الصفحة
                    var canvas = new Canvas
                    {
                        Width = A4_WIDTH_DIP,
                        Height = A4_HEIGHT_DIP
                    };
                    canvas.Children.Add(pageNumberContainer);

                    // إضافة Canvas للصفحة
                    fixedPage.Children.Add(canvas);

                    // إنشاء PageContent وإضافته للمستند
                    var pageContent = new PageContent();
                    ((IAddChild)pageContent).AddChild(fixedPage);
                    fixedDocument.Pages.Add(pageContent);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مستند طباعة بـ {fixedDocument.Pages.Count} صفحة");
                return fixedDocument;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء مستند الطباعة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// البحث عن الصفحات في ReportView الأصلي بناءً على الفواصل
        /// كل فاصل يعني نهاية صفحة وبداية صفحة جديدة
        /// </summary>
        private static List<Border> FindPrintPages(ReportView reportView)
        {
            var pages = new List<Border>();

            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 البحث عن الصفحات بناءً على الفواصل...");

                // البحث عن ScrollViewer الذي يحتوي على المحتوى
                var scrollViewer = FindChild<ScrollViewer>(reportView);
                if (scrollViewer == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ لم يتم العثور على ScrollViewer");
                    return pages;
                }

                // البحث عن StackPanel الرئيسي داخل ScrollViewer
                var mainStackPanel = FindChild<StackPanel>(scrollViewer);
                if (mainStackPanel == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ لم يتم العثور على StackPanel الرئيسي");
                    return pages;
                }

                System.Diagnostics.Debug.WriteLine($"📋 تم العثور على StackPanel مع {mainStackPanel.Children.Count} عنصر");

                // تحليل العناصر للعثور على الصفحات والفواصل
                foreach (UIElement child in mainStackPanel.Children)
                {
                    if (child is Border border)
                    {
                        // التحقق من أن هذا Border هو صفحة وليس فاصل
                        bool isPageSeparator = IsPageSeparator(border);
                        bool isContentPage = IsContentPage(border);

                        if (isContentPage)
                        {
                            pages.Add(border);
                            System.Diagnostics.Debug.WriteLine($"📄 صفحة {pages.Count}: تم العثور على صفحة محتوى");
                        }
                        else if (isPageSeparator)
                        {
                            System.Diagnostics.Debug.WriteLine($"➖ فاصل: تم العثور على فاصل بين الصفحات");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {pages.Count} صفحة إجمالية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن الصفحات: {ex.Message}");
            }

            return pages;
        }

        /// <summary>
        /// التحقق من أن Border هو فاصل بين الصفحات
        /// </summary>
        private static bool IsPageSeparator(Border border)
        {
            // الفاصل له خصائص محددة: ارتفاع 30، خلفية شفافة
            return border.Height == 30 &&
                   border.Background == Brushes.Transparent;
        }

        /// <summary>
        /// التحقق من أن Border يحتوي على محتوى صفحة
        /// </summary>
        private static bool IsContentPage(Border border)
        {
            // صفحة المحتوى لها Style محدد وليست فاصل
            return border.Style != null &&
                   !IsPageSeparator(border) &&
                   border.Child != null;
        }

        /// <summary>
        /// البحث عن عنصر فرعي من نوع معين في الشجرة المرئية
        /// </summary>
        private static T FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            // البحث المباشر في الأطفال
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T result)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {typeof(T).Name}");
                    return result;
                }

                // البحث العميق في الأطفال
                var childOfChild = FindChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }
    }
}
