using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// إصلاح جدول خط السير الميدانية - حذف وإعادة إنشاء
    /// </summary>
    public static class FixFieldVisitItinerariesTable
    {
        public static async Task ApplyAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء إصلاح جدول FieldVisitItineraries...");

                // التحقق من وجود الجدول
                var tableExists = await CheckTableExistsAsync(context);
                
                if (tableExists)
                {
                    System.Diagnostics.Debug.WriteLine("📋 الجدول موجود - سيتم حذفه وإعادة إنشاؤه");
                    
                    // حذف الجدول
                    await context.Database.ExecuteSqlRawAsync("DROP TABLE IF EXISTS [FieldVisitItineraries]");
                    System.Diagnostics.Debug.WriteLine("❌ تم حذف الجدول القديم");
                }

                // إعادة إنشاء الجدول
                var createTableSql = @"
                    CREATE TABLE [FieldVisitItineraries] (
                        [Id] int IDENTITY(1,1) NOT NULL,
                        [FieldVisitId] int NOT NULL,
                        [DayNumber] int NOT NULL,
                        [ItineraryText] nvarchar(1000) NOT NULL,
                        [CreatedAt] datetime2 NOT NULL DEFAULT (GETDATE()),
                        [UpdatedAt] datetime2 NULL,
                        [Notes] nvarchar(500) NULL,
                        [IsActive] bit NOT NULL DEFAULT (1),
                        CONSTRAINT [PK_FieldVisitItineraries] PRIMARY KEY ([Id]),
                        CONSTRAINT [FK_FieldVisitItineraries_FieldVisits] FOREIGN KEY ([FieldVisitId]) 
                            REFERENCES [FieldVisits] ([Id]) ON DELETE CASCADE
                    );

                    CREATE INDEX [IX_FieldVisitItineraries_FieldVisitId] ON [FieldVisitItineraries] ([FieldVisitId]);
                    CREATE INDEX [IX_FieldVisitItineraries_IsActive] ON [FieldVisitItineraries] ([IsActive]);
                ";

                await context.Database.ExecuteSqlRawAsync(createTableSql);
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء الجدول الجديد بنجاح");

                System.Diagnostics.Debug.WriteLine("🎉 تم إصلاح جدول FieldVisitItineraries بنجاح!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إصلاح جدول FieldVisitItineraries: {ex.Message}");
                throw;
            }
        }

        private static async Task<bool> CheckTableExistsAsync(ApplicationDbContext context)
        {
            try
            {
                using var command = context.Database.GetDbConnection().CreateCommand();
                command.CommandText = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FieldVisitItineraries'";

                await context.Database.OpenConnectionAsync();
                var result = await command.ExecuteScalarAsync();

                return Convert.ToInt32(result) > 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
