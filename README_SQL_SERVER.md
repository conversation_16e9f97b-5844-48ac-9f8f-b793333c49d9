# تحويل النظام إلى SQL Server

## نظرة عامة
تم تحديث نظام إدارة الزيارات الميدانية ليدعم **SQL Server** بالإضافة إلى **SQLite**. النظام الآن يمكنه:

- ✅ العمل مع **SQL Server** (الافتراضي)
- ✅ العمل مع **SQLite** (للاختبار والتطوير)
- ✅ **نقل البيانات تلقائياً** من SQLite إلى SQL Server
- ✅ **واجهة إعدادات** سهلة لتخصيص قاعدة البيانات

## المتطلبات

### لاستخدام SQL Server:
1. **SQL Server** مثبت ويعمل (SQL Server Express كافي)
2. **Windows Authentication** أو **SQL Server Authentication**
3. صلاحيات إنشاء قواعد البيانات

### لاستخدام SQLite:
- لا توجد متطلبات إضافية (يعمل مباشرة)

## طريقة الاستخدام

### 1. التشغيل الأول
```bash
# تشغيل النظام مع SQL Server (الافتراضي)
dotnet run

# أو استخدام ملف التشغيل
run_sqlserver.bat
```

### 2. إعدادات قاعدة البيانات
- افتح النظام
- اضغط على زر **"قاعدة البيانات"** في القائمة الجانبية
- اختر نوع قاعدة البيانات:
  - **SQL Server**: للاستخدام الإنتاجي
  - **SQLite**: للاختبار والتطوير

### 3. إعدادات SQL Server
في نافذة الإعدادات:
- **اسم الخادم**: `localhost` (أو اسم الخادم)
- **اسم قاعدة البيانات**: `SFDSYS`
- **المصادقة**: 
  - ✅ **Windows Authentication** (مستحسن)
  - أو **SQL Server Authentication** مع اسم مستخدم وكلمة مرور

### 4. اختبار الاتصال
- اضغط **"اختبار الاتصال"** للتأكد من صحة الإعدادات
- إذا نجح الاختبار، اضغط **"حفظ وتطبيق"**

## نقل البيانات التلقائي

عند التحويل من SQLite إلى SQL Server:

1. **النظام ينقل تلقائياً** جميع البيانات الموجودة
2. **ينشئ نسخة احتياطية** من ملف SQLite
3. **يحافظ على جميع العلاقات** والبيانات
4. **لا تفقد أي بيانات**

### البيانات المنقولة:
- ✅ المستخدمين والصلاحيات
- ✅ السائقين والمركبات
- ✅ الزيارات الميدانية
- ✅ المشاريع والقطاعات
- ✅ التقارير والمرفقات
- ✅ جميع الإعدادات

## متغيرات البيئة

يمكن تخصيص الإعدادات باستخدام متغيرات البيئة:

```bash
# إعدادات SQL Server
set SQL_SERVER_NAME=localhost
set SQL_DATABASE_NAME=SFDSYS
set SQL_USE_WINDOWS_AUTH=true

# إعدادات SQL Authentication (إذا لزم الأمر)
set SQL_USERNAME=sa
set SQL_PASSWORD=your_password
```

## ملفات التشغيل

### `run_sqlserver.bat`
```bash
# تشغيل النظام مع SQL Server
run_sqlserver.bat
```

### `run.bat` (الأصلي)
```bash
# تشغيل النظام مع SQLite
run.bat
```

## استكشاف الأخطاء

### مشكلة الاتصال بـ SQL Server
1. تأكد من تشغيل SQL Server
2. تحقق من اسم الخادم
3. تأكد من الصلاحيات
4. جرب Windows Authentication أولاً

### مشكلة في نقل البيانات
1. تحقق من وجود ملف SQLite في مجلد `Data`
2. تأكد من صلاحيات الكتابة في SQL Server
3. راجع رسائل الخطأ في النظام

### العودة إلى SQLite
1. افتح إعدادات قاعدة البيانات
2. اختر **SQLite**
3. احفظ الإعدادات
4. أعد تشغيل النظام

## الملفات الجديدة

```
📁 Data/
├── DatabaseConfig.cs          # إعدادات قاعدة البيانات
├── ApplicationDbContext.cs    # محدث لدعم SQL Server

📁 Services/
├── DataMigrationService.cs    # خدمة نقل البيانات
├── SqliteDataService.cs       # محدث لدعم النوعين

📁 Views/
├── DatabaseConfigWindow.xaml     # نافذة الإعدادات
├── DatabaseConfigWindow.xaml.cs  # كود الإعدادات

📁 Migrations/
├── [timestamp]_InitialSqlServerMigration.cs  # Migration جديد

📄 run_sqlserver.bat           # ملف تشغيل SQL Server
📄 README_SQL_SERVER.md        # هذا الملف
```

## الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من رسائل الخطأ في النظام
2. راجع ملف الـ Debug Output
3. تأكد من متطلبات SQL Server
4. جرب العودة إلى SQLite مؤقتاً

---

**ملاحظة**: النظام يحتفظ بدعم SQLite الكامل، لذا يمكنك التبديل بين النوعين حسب الحاجة.
