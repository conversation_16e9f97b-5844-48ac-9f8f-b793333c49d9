using System;
using System.Threading.Tasks;
using System.Windows;
using DriverManagementSystem.Data;
using DriverManagementSystem.Services;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem
{
    /// <summary>
    /// أداة استعادة قاعدة البيانات
    /// </summary>
    public static class DatabaseRestorer
    {
        /// <summary>
        /// استعادة قاعدة البيانات وإعادة تحميل البيانات
        /// </summary>
        public static async Task<bool> RestoreDatabaseAsync()
        {
            try
            {
                Console.WriteLine("🔄 بدء استعادة قاعدة البيانات...");

                using var context = new ApplicationDbContext();
                
                // التحقق من الاتصال بقاعدة البيانات
                var canConnect = await context.Database.CanConnectAsync();
                Console.WriteLine($"📡 حالة الاتصال بقاعدة البيانات: {(canConnect ? "متصل" : "غير متصل")}");

                if (!canConnect)
                {
                    Console.WriteLine("❌ لا يمكن الاتصال بقاعدة البيانات");
                    return false;
                }

                // عدد الزيارات الميدانية
                var visitsCount = await context.FieldVisits.CountAsync();
                Console.WriteLine($"📊 عدد الزيارات الميدانية: {visitsCount}");

                // عدد السائقين
                var driversCount = await context.Drivers.CountAsync();
                Console.WriteLine($"🚗 عدد السائقين: {driversCount}");

                // عدد المشاريع
                var projectsCount = await context.Projects.CountAsync();
                Console.WriteLine($"🏗️ عدد المشاريع: {projectsCount}");

                // عدد القطاعات
                var sectorsCount = await context.Sectors.CountAsync();
                Console.WriteLine($"🏢 عدد القطاعات: {sectorsCount}");

                // عدد الموظفين
                var officersCount = await context.Officers.CountAsync();
                Console.WriteLine($"👥 عدد الموظفين: {officersCount}");

                // إذا كانت البيانات فارغة، استعد البيانات التجريبية
                if (visitsCount == 0 || driversCount == 0)
                {
                    Console.WriteLine("⚠️ البيانات فارغة، سيتم استعادة البيانات التجريبية...");
                    await RestoreSampleDataAsync(context);
                }

                Console.WriteLine("✅ تم استعادة قاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في استعادة قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// استعادة البيانات التجريبية
        /// </summary>
        private static async Task RestoreSampleDataAsync(ApplicationDbContext context)
        {
            try
            {
                // استعادة البيانات باستخدام الخدمة الموجودة
                var dataService = new SqliteDataService();
                await dataService.RestoreSampleDataAsync();
                
                Console.WriteLine("✅ تم استعادة البيانات التجريبية");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في استعادة البيانات التجريبية: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين قاعدة البيانات بالكامل
        /// </summary>
        public static async Task<bool> ResetDatabaseAsync()
        {
            try
            {
                Console.WriteLine("🔄 بدء إعادة تعيين قاعدة البيانات...");

                using var context = new ApplicationDbContext();
                
                // حذف قاعدة البيانات
                await context.Database.EnsureDeletedAsync();
                Console.WriteLine("🗑️ تم حذف قاعدة البيانات القديمة");

                // إنشاء قاعدة البيانات من جديد
                await context.Database.EnsureCreatedAsync();
                Console.WriteLine("🆕 تم إنشاء قاعدة البيانات الجديدة");

                // استعادة البيانات التجريبية
                await RestoreSampleDataAsync(context);

                Console.WriteLine("✅ تم إعادة تعيين قاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إعادة تعيين قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// عرض معلومات قاعدة البيانات
        /// </summary>
        public static async Task ShowDatabaseInfoAsync()
        {
            try
            {
                using var context = new ApplicationDbContext();
                
                Console.WriteLine("📊 === معلومات قاعدة البيانات ===");
                
                var visitsCount = await context.FieldVisits.CountAsync();
                var driversCount = await context.Drivers.CountAsync();
                var projectsCount = await context.Projects.CountAsync();
                var sectorsCount = await context.Sectors.CountAsync();
                var officersCount = await context.Officers.CountAsync();
                var usersCount = await context.Users.CountAsync();

                Console.WriteLine($"📋 الزيارات الميدانية: {visitsCount}");
                Console.WriteLine($"🚗 السائقين: {driversCount}");
                Console.WriteLine($"🏗️ المشاريع: {projectsCount}");
                Console.WriteLine($"🏢 القطاعات: {sectorsCount}");
                Console.WriteLine($"👥 الموظفين: {officersCount}");
                Console.WriteLine($"👤 المستخدمين: {usersCount}");
                Console.WriteLine("================================");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في عرض معلومات قاعدة البيانات: {ex.Message}");
            }
        }
    }
}
