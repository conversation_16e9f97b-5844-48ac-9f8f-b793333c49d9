using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    public interface IDataService : IDisposable
    {
        // Drivers methods
        Task<List<Driver>> GetDriversAsync();
        Task<bool> AddDriverAsync(Driver driver);
        Task<bool> UpdateDriverAsync(Driver driver);
        Task<bool> DeleteDriverAsync(int driverId);

        // Sectors methods
        Task<List<Sector>> GetSectorsAsync();
        Task<bool> AddSectorAsync(Sector sector);
        Task<Sector?> GetSectorByCodeAsync(string sectorCode);

        // Officers methods
        Task<List<Officer>> GetOfficersAsync();
        Task<List<Officer>> GetOfficersBySectorAsync(int sectorId);
        Task<bool> AddOfficer<PERSON>ync(Officer officer);
        Task<bool> UpdateOfficerAsync(Officer officer);
        Task<bool> DeleteOfficerAsync(int officerId);
        Task<Officer?> GetOfficerByCodeAsync(string officerCode);

        // Vehicles methods
        Task<List<Vehicle>> GetVehiclesAsync();
        Task<bool> AddVehicleAsync(Vehicle vehicle);

        // Field Visits methods
        Task<List<FieldVisit>> GetFieldVisitsAsync();
        Task<(bool Success, List<string> Errors)> AddFieldVisitAsync(FieldVisit fieldVisit);
        Task<bool> UpdateFieldVisitAsync(FieldVisit fieldVisit);
        Task<bool> DeleteFieldVisitAsync(int fieldVisitId);
        Task<bool> ClearAllFieldVisitsAsync();
        Task<FieldVisit?> GetFieldVisitByDriverContractAsync(string driverContract);

        // Projects methods
        Task<List<Project>> GetProjectsAsync();
        Task<Project?> GetProjectByNumberAsync(string projectNumber);
        Task<bool> AddProjectAsync(Project project);
        Task<bool> UpdateProjectAsync(Project project);
        Task<bool> DeleteProjectAsync(int projectId);

        // Driver Quotes methods
        Task<List<DriverQuote>> GetDriverQuotesAsync();
        Task<List<DriverQuote>> GetDriverQuotesByStatusAsync(QuoteStatus status);
        Task<bool> AddDriverQuoteAsync(DriverQuote quote);
        Task<bool> UpdateDriverQuoteAsync(DriverQuote quote);
        Task<bool> DeleteDriverQuoteAsync(int quoteId);
        Task<bool> UpdateDriverQuoteStatusAsync(int quoteId, QuoteStatus status);
        Task<DriverQuote?> GetDriverQuoteByDriverIdAsync(int driverId);

        // Refresh methods
        Task RefreshAllDataAsync();

        // Offers methods
        Task<bool> SaveVisitOffersAsync(string visitNumber, string offersText, int daysCount);
        Task<bool> SaveWinnerDriverMessageAsync(string visitNumber, string messageText);
        Task<List<DriverOffer>> GetVisitOffersAsync(string visitNumber);
        Task<bool> DeleteVisitOffersAsync(string visitNumber);

        // Report data methods
        Task<List<PriceOfferItem>> GetPriceOffersByVisitIdAsync(int visitId);
        Task<SelectedVehicleData> GetSelectedVehicleByVisitIdAsync(int visitId);

        // Field Visit Projects methods
        Task<(bool Success, List<string> Errors)> SaveFieldVisitProjectsAsync(int fieldVisitId, List<FieldVisitProject> projects);
        Task<List<FieldVisitProject>> GetFieldVisitProjectsAsync(int fieldVisitId);
        Task<List<FieldVisitProject>> GetProjectsByVisitIdAsync(int visitId);

        // Field Visit Visitors methods
        Task<List<FieldVisitor>> GetFieldVisitVisitorsAsync(int fieldVisitId);

    }
}
