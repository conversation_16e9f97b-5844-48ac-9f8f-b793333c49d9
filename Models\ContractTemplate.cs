using System;
using System.ComponentModel.DataAnnotations;

namespace DriverManagementSystem.Models
{
    public class ContractTemplate
    {
        [Key]
        public int Id { get; set; }
        
        public string ContractIntroduction { get; set; } = "أنه في يوم {ContractDateArabic} الموافق {ContractDate} م بأمانة العاصمة صنعاء، تم إبرام هذا العقد بين كل من:";
        
        public string FirstPartyTemplate { get; set; } = "الاسم: {DriverName}، رقم البطاقة الشخصية: {NationalId}، مكان الإصدار: {IssuePlace}، تاريخ الإصدار: {IssueDate}";
        
        public string SecondPartyTemplate { get; set; } = "الصندوق الاجتماعي للتنمية - المركز الرئيسي ومقره شارع الشهيد إبراهيم الحمدي (شارع الستين الجنوبي) مقابل الجهاز المركزي للرقابة والمحاسبة، ويمثله في هذا العقد الأستاذ عبدالله علي الديلمي بصفته المدير التنفيذي للصندوق.";
        
        public string VehicleSpecsTemplate { get; set; } = "أجر الطرف الأول للطرف الثاني سيارة بالمواصفات التالية: نوع السيارة: {VehicleType}، سنة الصنع: {ManufactureYear}، لون السيارة: {VehicleColor}، رقم اللوحة: {VehicleNumber}، رقم رخصة التسيير: {LicenseNumber}، تاريخ إصدار الرخصة: {LicenseIssueDate}";
        
        public string PurposeTemplate { get; set; } = "اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل (سيارة مع سائقها) لمرافقة الأخ/ {VisitConductor}، صفته {VisitConductorRank} أثناء تنفيذ المهام الميدانية المطلوبة منه.";
        
        public string DurationTemplate { get; set; } = "تاريخ بداية المهمة: {StartDateArabic} الموافق {StartDate}م، تاريخ انتهاء المهمة: {EndDateArabic} الموافق {EndDate}م، إجمالي عدد الأيام: {DaysCount} أيام. تسير المهمة بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد.";
        
        public string PriceTemplate { get; set; } = "اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره {TotalPrice} ريال يمني، وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة.";
        
        public string OwnershipTemplate { get; set; } = "يقر الطرف الأول بموجب هذا العقد بأنه المالك الشرعي لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية سارية المفعول وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير.";
        
        public string ObligationsTemplate { get; set; } = "يلتزم الطرف الأول بموجب هذا العقد بأن ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد، والامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة، والحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه، وعدم نقل أي ركاب آخرين عدا الطرف الثاني أثناء أداء المهام الميدانية، واصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة، والصيانة الدورية لوسيلة النقل وفحصها قبل تنفيذ المهمة، وسرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة، واصطحاب أدوات السلامة مثل طفاية الحريق وصندوق إسعافات أولية ومثلث التحذير وعدة صيانة السيارة.";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
    }
}
