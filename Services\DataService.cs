using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    public class DataService : IDataService
    {
        private readonly SqliteDataService _sqliteService;

        public DataService()
        {
            _sqliteService = new SqliteDataService();
            System.Diagnostics.Debug.WriteLine("🗄️ DataService using SQLite backend initialized successfully!");
        }

        // Drivers methods
        public async Task<List<Driver>> GetDriversAsync()
        {
            return await _sqliteService.GetDriversAsync();
        }

        public async Task<bool> AddDriverAsync(Driver driver)
        {
            return await _sqliteService.AddDriverAsync(driver);
        }

        public async Task<bool> UpdateDriverAsync(Driver driver)
        {
            return await _sqliteService.UpdateDriverAsync(driver);
        }

        public async Task<bool> DeleteDriverAsync(int driverId)
        {
            return await _sqliteService.DeleteDriverAsync(driverId);
        }

        // Sectors methods
        public async Task<List<Sector>> GetSectorsAsync()
        {
            return await _sqliteService.GetSectorsAsync();
        }

        public async Task<bool> AddSectorAsync(Sector sector)
        {
            return await _sqliteService.AddSectorAsync(sector);
        }

        public async Task<Sector?> GetSectorByCodeAsync(string sectorCode)
        {
            return await _sqliteService.GetSectorByCodeAsync(sectorCode);
        }

        // Officers methods
        public async Task<List<Officer>> GetOfficersAsync()
        {
            return await _sqliteService.GetOfficersAsync();
        }

        public async Task<List<Officer>> GetOfficersBySectorAsync(int sectorId)
        {
            return await _sqliteService.GetOfficersBySectorAsync(sectorId);
        }

        public async Task<bool> AddOfficerAsync(Officer officer)
        {
            return await _sqliteService.AddOfficerAsync(officer);
        }

        public async Task<bool> UpdateOfficerAsync(Officer officer)
        {
            return await _sqliteService.UpdateOfficerAsync(officer);
        }

        public async Task<bool> DeleteOfficerAsync(int officerId)
        {
            return await _sqliteService.DeleteOfficerAsync(officerId);
        }

        public async Task<Officer?> GetOfficerByCodeAsync(string officerCode)
        {
            return await _sqliteService.GetOfficerByCodeAsync(officerCode);
        }

        // Vehicles methods
        public async Task<List<Vehicle>> GetVehiclesAsync()
        {
            return await _sqliteService.GetVehiclesAsync();
        }

        public async Task<bool> AddVehicleAsync(Vehicle vehicle)
        {
            return await _sqliteService.AddVehicleAsync(vehicle);
        }

        // Field Visits methods
        public async Task<List<FieldVisit>> GetFieldVisitsAsync()
        {
            return await _sqliteService.GetFieldVisitsAsync();
        }

        public async Task<(bool Success, List<string> Errors)> AddFieldVisitAsync(FieldVisit fieldVisit)
        {
            return await _sqliteService.AddFieldVisitAsync(fieldVisit);
        }

        public async Task<bool> UpdateFieldVisitAsync(FieldVisit fieldVisit)
        {
            return await _sqliteService.UpdateFieldVisitAsync(fieldVisit);
        }

        public async Task<bool> DeleteFieldVisitAsync(int fieldVisitId)
        {
            return await _sqliteService.DeleteFieldVisitAsync(fieldVisitId);
        }

        public async Task<bool> ClearAllFieldVisitsAsync()
        {
            return await _sqliteService.ClearAllFieldVisitsAsync();
        }

        public async Task<FieldVisit?> GetFieldVisitByDriverContractAsync(string driverContract)
        {
            return await _sqliteService.GetFieldVisitByDriverContractAsync(driverContract);
        }

        // Projects methods
        public async Task<List<Project>> GetProjectsAsync()
        {
            return await _sqliteService.GetProjectsAsync();
        }

        public async Task<Project?> GetProjectByNumberAsync(string projectNumber)
        {
            return await _sqliteService.GetProjectByNumberAsync(projectNumber);
        }

        public async Task<bool> AddProjectAsync(Project project)
        {
            return await _sqliteService.AddProjectAsync(project);
        }

        public async Task<bool> UpdateProjectAsync(Project project)
        {
            return await _sqliteService.UpdateProjectAsync(project);
        }

        public async Task<bool> DeleteProjectAsync(int projectId)
        {
            return await _sqliteService.DeleteProjectAsync(projectId);
        }

        // Driver Quotes methods
        public async Task<List<DriverQuote>> GetDriverQuotesAsync()
        {
            return await _sqliteService.GetDriverQuotesAsync();
        }

        public async Task<List<DriverQuote>> GetDriverQuotesByStatusAsync(QuoteStatus status)
        {
            return await _sqliteService.GetDriverQuotesByStatusAsync(status);
        }

        public async Task<bool> AddDriverQuoteAsync(DriverQuote quote)
        {
            return await _sqliteService.AddDriverQuoteAsync(quote);
        }

        public async Task<bool> UpdateDriverQuoteAsync(DriverQuote quote)
        {
            return await _sqliteService.UpdateDriverQuoteAsync(quote);
        }

        public async Task<bool> DeleteDriverQuoteAsync(int quoteId)
        {
            return await _sqliteService.DeleteDriverQuoteAsync(quoteId);
        }

        public async Task<bool> UpdateDriverQuoteStatusAsync(int quoteId, QuoteStatus status)
        {
            return await _sqliteService.UpdateDriverQuoteStatusAsync(quoteId, status);
        }

        public async Task<DriverQuote?> GetDriverQuoteByDriverIdAsync(int driverId)
        {
            return await _sqliteService.GetDriverQuoteByDriverIdAsync(driverId);
        }

        // Refresh methods
        public async Task RefreshAllDataAsync()
        {
            await _sqliteService.RefreshAllDataAsync();
        }

        // Offers methods
        public async Task<bool> SaveVisitOffersAsync(string visitNumber, string offersText, int daysCount)
        {
            return await _sqliteService.SaveVisitOffersAsync(visitNumber, offersText, daysCount);
        }

        public async Task<bool> SaveWinnerDriverMessageAsync(string visitNumber, string messageText)
        {
            return await _sqliteService.SaveWinnerDriverMessageAsync(visitNumber, messageText);
        }

        public async Task<List<DriverOffer>> GetVisitOffersAsync(string visitNumber)
        {
            return await _sqliteService.GetVisitOffersAsync(visitNumber);
        }

        public async Task<bool> DeleteVisitOffersAsync(string visitNumber)
        {
            return await _sqliteService.DeleteVisitOffersAsync(visitNumber);
        }

        // Report data methods
        public async Task<List<PriceOfferItem>> GetPriceOffersByVisitIdAsync(int visitId)
        {
            return await _sqliteService.GetPriceOffersByVisitIdAsync(visitId);
        }

        public async Task<SelectedVehicleData> GetSelectedVehicleByVisitIdAsync(int visitId)
        {
            return await _sqliteService.GetSelectedVehicleByVisitIdAsync(visitId);
        }

        // Field Visit Projects methods
        public async Task<(bool Success, List<string> Errors)> SaveFieldVisitProjectsAsync(int fieldVisitId, List<FieldVisitProject> projects)
        {
            return await _sqliteService.SaveFieldVisitProjectsAsync(fieldVisitId, projects);
        }

        public async Task<List<FieldVisitProject>> GetFieldVisitProjectsAsync(int fieldVisitId)
        {
            return await _sqliteService.GetFieldVisitProjectsAsync(fieldVisitId);
        }

        public async Task<List<FieldVisitProject>> GetProjectsByVisitIdAsync(int visitId)
        {
            return await _sqliteService.GetProjectsByVisitIdAsync(visitId);
        }

        public async Task<List<FieldVisitor>> GetFieldVisitVisitorsAsync(int fieldVisitId)
        {
            return await _sqliteService.GetFieldVisitVisitorsAsync(fieldVisitId);
        }



        public void Dispose()
        {
            _sqliteService?.Dispose();
        }
    }
}
