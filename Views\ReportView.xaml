<UserControl x:Class="DriverManagementSystem.Views.ReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- Converter for boolean to color -->
        <local:BoolToColorConverter x:Key="BoolToColorConverter"/>

        <!-- Style for buttons -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="4"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F8F9FA">
        <!-- Header Section -->
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#DEE2E6" BorderThickness="0,0,0,1" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="📄 تقرير الزيارة الميدانية"
                         FontSize="16" FontWeight="Bold" Foreground="#495057" VerticalAlignment="Center"/>

                    <!-- رقم الزيارة -->
                    <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4" Margin="20,0,0,0">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                            <TextBlock FontSize="12" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                            <Run Text="رقم الزيارة: "/>
                            <Run Text="{Binding ReportData.VisitNumber, FallbackValue=---}"/>
                            </TextBlock>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- Action Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="📄 معاينة الطباعة" Style="{StaticResource ModernButtonStyle}"
                           Click="PrintPreviewButton_Click" Background="#28A745"/>
                    <Button Content="🖨️ طباعة" Style="{StaticResource ModernButtonStyle}"
                           Click="PrintReportButton_Click" Background="#DC3545"/>
                    <Button Content="📱 توثيق الرسائل" Style="{StaticResource ModernButtonStyle}"
                           Click="MessageDocumentationButton_Click" Background="#FFC107" Foreground="#212529"/>
                    <Button Content="📋 التكليف" Style="{StaticResource ModernButtonStyle}"
                           Click="AssignmentButton_Click" Background="#6F42C1"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                <!-- Report Content -->
                <Border Background="White" CornerRadius="8" Padding="30" Margin="0,0,0,20"
                       BorderBrush="#DEE2E6" BorderThickness="1">
                    <StackPanel>
                        <!-- Report Header -->
                        <Grid Margin="0,0,0,30">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Logo on the left -->
                            <Border Grid.Column="0" BorderBrush="#DDDDDD" BorderThickness="1" Padding="8,5" Background="White">
                                <Image Source="C:\Users\<USER>\Desktop\sys\SFD\logos\sfd.png" Width="60" Height="60" Stretch="Uniform"/>
                            </Border>

                            <!-- Empty space in center -->
                            <TextBlock Grid.Column="1"/>

                            <!-- Sector on the right with Icon -->
                            <Border Grid.Column="2" BorderBrush="#DDDDDD" BorderThickness="1" Padding="8,5" Background="White">
                                <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"><Run Text="🏢 القطاع: "/><Run Text=" "/><Run Text="{Binding ReportData.SectorName, FallbackValue=الصحة والحماية الاجتماعية}"/></TextBlock>
                            </Border>
                        </Grid>

                        <!-- Report Title -->
                        <TextBlock Text="{Binding ReportData.ReportTitle}" FontSize="18" FontWeight="Bold"
                                 HorizontalAlignment="Center" Margin="0,0,0,20" Foreground="#2C3E50"/>

                        <!-- Report Date -->
                        <TextBlock HorizontalAlignment="Center" Margin="0,0,0,30" FontSize="12" Foreground="#666666">
                        <Run Text="تاريخ التقرير: "/>
                        <Run Text="{Binding ReportData.ReportDate}"/>
                        </TextBlock>

                        <!-- Report Sections -->
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- القسم الأول: معلومات الزيارة -->
                            <Border Grid.Row="0" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,0,10" Background="#F8F9FA" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="معلومات الزيارة" FontWeight="Bold" FontSize="14"
                                         Foreground="#2C3E50" Margin="0,0,0,10"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                            <TextBlock Text="الغرض من الزيارة:" FontWeight="Bold" FontSize="12"
                                                 Foreground="#2C3E50" Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding ReportData.MissionPurpose}" FontSize="11"
                                                 Foreground="#666666" TextWrapping="Wrap" Background="#F5F5F5" Padding="8"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="طبيعة الزيارة:" FontWeight="Bold" FontSize="12"
                                                 Foreground="#2C3E50" Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding ReportData.VisitNature}" FontSize="11"
                                                 Foreground="#666666" TextWrapping="Wrap" Background="#F5F5F5" Padding="8"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- القسم الثاني: التواريخ -->
                            <Border Grid.Row="1" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,0,10" Background="#F8F9FA" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="تواريخ الزيارة" FontWeight="Bold" FontSize="14"
                                         Foreground="#2C3E50" Margin="0,0,0,10"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                            <TextBlock Text="تاريخ المغادرة:" FontWeight="Bold" FontSize="12"
                                                 Foreground="#2C3E50" Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding ReportData.DepartureDate}" FontSize="11"
                                                 Foreground="#666666" Background="#F5F5F5" Padding="8"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="1" Margin="0,0,10,0">
                                            <TextBlock Text="تاريخ العودة:" FontWeight="Bold" FontSize="12"
                                                 Foreground="#2C3E50" Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding ReportData.ReturnDate}" FontSize="11"
                                                 Foreground="#666666" Background="#F5F5F5" Padding="8"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="2">
                                            <TextBlock Text="عدد الأيام:" FontWeight="Bold" FontSize="12"
                                                 Foreground="#2C3E50" Margin="0,0,0,5"/>
                                            <TextBlock Text="{Binding ReportData.DaysCount}" FontSize="11"
                                                 Foreground="#666666" Background="#F5F5F5" Padding="8"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>

                            <!-- القسم الثالث: خط السير -->
                            <Border Grid.Row="2" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,0,10" Background="#F8F9FA" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="خط السير" FontWeight="Bold" FontSize="14"
                                         Foreground="#2C3E50" Margin="0,0,0,10"/>
                                    <Border BorderBrush="#CCCCCC" BorderThickness="1" Background="White" MinHeight="100">
                                        <ItemsControl ItemsSource="{Binding ReportData.Itinerary}">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border BorderBrush="#EEEEEE" BorderThickness="0,0,0,1" Padding="10,8">
                                                        <StackPanel Orientation="Horizontal">
                                                            <TextBlock Text="{Binding DayNumber, StringFormat=اليوم {0}:}" FontWeight="Bold"
                                                                     Margin="0,0,10,0" Foreground="#2C3E50" Width="80"/>
                                                            <TextBlock Text="{Binding Plan}" FontSize="11" Foreground="#666666" TextWrapping="Wrap"/>
                                                        </StackPanel>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- القسم الثالث والنصف: عدد الأيام ورقم المشروع -->
                            <Grid Grid.Row="3" Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- عدد الأيام -->
                                <Border Grid.Column="0" BorderBrush="#4CAF50" BorderThickness="2" Margin="0,0,5,0" Background="#E8F5E8" Padding="15">
                                    <StackPanel>
                                        <TextBlock Text="عدد أيام الزيارة" FontWeight="Bold" FontSize="14"
                                                 Foreground="#2E7D32" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Border Background="#4CAF50" CornerRadius="20" Padding="15,8" MinWidth="80">
                                                <TextBlock Text="{Binding ReportData.DaysCount}" FontWeight="Bold" FontSize="20"
                                                         Foreground="White" HorizontalAlignment="Center"/>
                                            </Border>
                                            <TextBlock Text="يوم" FontWeight="Bold" FontSize="16"
                                                     Foreground="#2E7D32" Margin="10,0,0,0" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>

                                <!-- رقم المشروع -->
                                <Border Grid.Column="1" BorderBrush="#2196F3" BorderThickness="2" Margin="5,0,0,0" Background="#E3F2FD" Padding="15">
                                    <StackPanel>
                                        <TextBlock Text="رقم المشروع" FontWeight="Bold" FontSize="14"
                                                 Foreground="#1976D2" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <Border Background="#2196F3" CornerRadius="20" Padding="15,8" MinWidth="120">
                                            <TextBlock Text="{Binding ReportData.ProjectNumber}" FontWeight="Bold" FontSize="16"
                                                     Foreground="White" HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                        </Border>
                                    </StackPanel>
                                </Border>
                            </Grid>



                            <!-- القسم الخامس: القائمين بالزيارة -->
                            <Border Grid.Row="5" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,0,10" Background="#F8F9FA" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="القائمين بالزيارة" FontWeight="Bold" FontSize="14"
                                         Foreground="#2C3E50" Margin="0,0,0,10"/>
                                    <Border BorderBrush="#CCCCCC" BorderThickness="1" Background="White">
                                        <ItemsControl ItemsSource="{Binding ReportData.Visitors}">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border BorderBrush="#EEEEEE" BorderThickness="0,0,0,1" Padding="10,8">
                                                        <StackPanel Orientation="Horizontal">
                                                            <TextBlock Text="•" FontWeight="Bold" Margin="0,0,8,0" Foreground="#2C3E50"/>
                                                            <TextBlock Text="{Binding Name}" FontWeight="Bold" Margin="0,0,10,0" Foreground="#2C3E50"/>
                                                            <TextBlock Text="{Binding Rank}" FontSize="10" Foreground="#666666"/>
                                                        </StackPanel>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- القسم الخامس: عروض الأسعار -->
                            <Border Grid.Row="5" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,0,10" Background="#F8F9FA" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="عروض الأسعار" FontWeight="Bold" FontSize="14"
                                         Foreground="#2C3E50" Margin="0,0,0,10"/>

                                    <!-- Enhanced Table Header -->
                                    <Border BorderBrush="Black" BorderThickness="2" Background="#E8F4FD" Height="36">
                                        <Grid MinHeight="35">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="60"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="120"/>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="80"/>
                                            </Grid.ColumnDefinitions>

                                            <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                                <TextBlock Text="م" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                            </Border>
                                            <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                                <TextBlock Text="اسم السائق" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                            </Border>
                                            <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                                <TextBlock Text="رقم الهاتف" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                            </Border>
                                            <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                                <TextBlock Text="السعر المقدم" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                            </Border>
                                            <Border Grid.Column="4" Padding="8,8">
                                                <TextBlock Text="الحالة" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                            </Border>
                                        </Grid>
                                    </Border>

                                    <!-- Enhanced Table Data -->
                                    <ItemsControl ItemsSource="{Binding ReportData.PriceOffers}">
                                        <!-- Empty State Message -->
                                        <ItemsControl.Style>
                                            <Style TargetType="ItemsControl">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding ReportData.PriceOffers.Count}" Value="0">
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate>
                                                                    <Border Background="#F8F9FA" Padding="20" Margin="2">
                                                                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                                                            <TextBlock Text="📋" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                                                            <TextBlock Text="لا توجد عروض أسعار لهذه الزيارة"
                                                                             FontSize="14" FontWeight="SemiBold"
                                                                             HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,0,0,5"/>
                                                                            <TextBlock Text="يرجى إضافة عروض الأسعار من نافذة الرسائل المهنية"
                                                                             FontSize="12" HorizontalAlignment="Center" Foreground="#ADB5BD"/>
                                                                        </StackPanel>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </ItemsControl.Style>

                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Border BorderBrush="Black" BorderThickness="2,0,2,1">
                                                    <Grid MinHeight="35" Background="White">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="60"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="120"/>
                                                            <ColumnDefinition Width="100"/>
                                                            <ColumnDefinition Width="80"/>
                                                        </Grid.ColumnDefinitions>

                                                        <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                            <TextBlock Text="{Binding SerialNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="Bold" Foreground="Black"/>
                                                        </Border>
                                                        <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                            <TextBlock Text="{Binding DriverName}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="SemiBold" Foreground="Black"/>
                                                        </Border>
                                                        <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                            <TextBlock Text="{Binding PhoneNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" Foreground="Black"/>
                                                        </Border>
                                                        <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                            <TextBlock Text="{Binding OfferedPrice, StringFormat={}{0:N0} ريال}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="Bold" Foreground="Black"/>
                                                        </Border>
                                                        <Border Grid.Column="4" Padding="8,6">
                                                            <TextBlock Text="{Binding Status}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="Bold"
                                                             Foreground="{Binding IsWinner, Converter={StaticResource BoolToColorConverter}}"/>
                                                        </Border>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>