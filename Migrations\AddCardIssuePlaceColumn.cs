using System;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// إضافة عمود مكان إصدار البطاقة إلى جدول السائقين
    /// </summary>
    public class AddCardIssuePlaceColumn
    {
        /// <summary>
        /// تطبيق التحديث
        /// </summary>
        public static async Task ApplyAsync(ApplicationDbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إضافة عمود مكان إصدار البطاقة...");

                // محاولة إضافة العمود - إذا كان موجوداً سيفشل الأمر
                try
                {
                    // استخدام SQL مباشر لإضافة العمود
                    var sql = "ALTER TABLE Drivers ADD COLUMN CardIssuePlace TEXT DEFAULT ''";

                    // تنفيذ الأمر مباشرة
                    await context.Database.ExecuteSqlRawAsync(sql);

                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة عمود CardIssuePlace بنجاح");
                }
                catch (Exception ex) when (ex.Message.Contains("duplicate column") || ex.Message.Contains("already exists"))
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ عمود CardIssuePlace موجود بالفعل");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة عمود CardIssuePlace: {ex.Message}");
                // لا نرمي الخطأ لأن العمود قد يكون موجوداً بالفعل
            }
        }

        /// <summary>
        /// التراجع عن التحديث (غير مدعوم في SQLite)
        /// </summary>
        public static async Task RollbackAsync(ApplicationDbContext context)
        {
            // في SQLite لا يمكن حذف عمود بسهولة
            // هذه الطريقة للتوافق فقط
            await Task.CompletedTask;
            System.Diagnostics.Debug.WriteLine("⚠️ التراجع عن إضافة العمود غير مدعوم في SQLite");
        }
    }
}
