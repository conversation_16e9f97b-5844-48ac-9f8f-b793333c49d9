using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// مركز تحكم موحد للأخطاء - يلتقط ويسجل ويترجم جميع الأخطاء في النظام
    /// </summary>
    public class ErrorHandlingService
    {
        private readonly string _logFilePath;
        private readonly string _errorDatabasePath;

        public ErrorHandlingService()
        {
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SFDSystem");
            Directory.CreateDirectory(appDataPath);
            
            _logFilePath = Path.Combine(appDataPath, "ErrorLog.txt");
            _errorDatabasePath = Path.Combine(appDataPath, "ErrorDatabase.json");
        }

        /// <summary>
        /// التقاط ومعالجة أي استثناء في النظام
        /// </summary>
        public ErrorResult HandleException(Exception ex, string context = "", string userAction = "")
        {
            var errorId = Guid.NewGuid().ToString("N")[..8];
            var timestamp = DateTime.Now;

            // 1. تسجيل الخطأ التقني الكامل
            LogTechnicalError(errorId, ex, context, userAction, timestamp);

            // 2. حفظ في قاعدة بيانات الأخطاء
            SaveToErrorDatabase(errorId, ex, context, userAction, timestamp);

            // 3. بناء رسالة بشرية للمستخدم
            var userMessage = BuildHumanFriendlyMessage(ex, errorId);

            return new ErrorResult
            {
                ErrorId = errorId,
                UserMessage = userMessage,
                TechnicalMessage = ex.Message,
                StackTrace = ex.StackTrace,
                Timestamp = timestamp,
                Context = context,
                UserAction = userAction
            };
        }

        /// <summary>
        /// تسجيل الخطأ التقني في ملف
        /// </summary>
        private void LogTechnicalError(string errorId, Exception ex, string context, string userAction, DateTime timestamp)
        {
            try
            {
                var logEntry = new StringBuilder();
                logEntry.AppendLine($"==================== خطأ {errorId} ====================");
                logEntry.AppendLine($"الوقت: {timestamp:yyyy-MM-dd HH:mm:ss}");
                logEntry.AppendLine($"السياق: {context}");
                logEntry.AppendLine($"إجراء المستخدم: {userAction}");
                logEntry.AppendLine($"نوع الخطأ: {ex.GetType().Name}");
                logEntry.AppendLine($"رسالة الخطأ: {ex.Message}");
                
                if (ex.InnerException != null)
                {
                    logEntry.AppendLine($"الخطأ الداخلي: {ex.InnerException.Message}");
                }
                
                logEntry.AppendLine($"StackTrace:");
                logEntry.AppendLine(ex.StackTrace);
                logEntry.AppendLine();

                File.AppendAllText(_logFilePath, logEntry.ToString(), Encoding.UTF8);
            }
            catch
            {
                // تجنب الأخطاء في نظام الأخطاء نفسه
            }
        }

        /// <summary>
        /// حفظ الخطأ في قاعدة بيانات JSON
        /// </summary>
        private void SaveToErrorDatabase(string errorId, Exception ex, string context, string userAction, DateTime timestamp)
        {
            try
            {
                var errorRecord = new ErrorRecord
                {
                    ErrorId = errorId,
                    Timestamp = timestamp,
                    Context = context,
                    UserAction = userAction,
                    ExceptionType = ex.GetType().Name,
                    Message = ex.Message,
                    InnerExceptionMessage = ex.InnerException?.Message,
                    StackTrace = ex.StackTrace
                };

                var errors = LoadErrorDatabase();
                errors.Add(errorRecord);

                // الاحتفاظ بآخر 1000 خطأ فقط
                if (errors.Count > 1000)
                {
                    errors.RemoveRange(0, errors.Count - 1000);
                }

                var json = JsonSerializer.Serialize(errors, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_errorDatabasePath, json, Encoding.UTF8);
            }
            catch
            {
                // تجنب الأخطاء في نظام الأخطاء نفسه
            }
        }

        /// <summary>
        /// بناء رسالة بشرية مفهومة للمستخدم
        /// </summary>
        private string BuildHumanFriendlyMessage(Exception ex, string errorId)
        {
            var message = new StringBuilder();
            message.AppendLine("❌ حدث خطأ في النظام:");
            message.AppendLine();

            // معالجة أخطاء قاعدة البيانات
            if (ex is DbUpdateException dbEx)
            {
                var dbError = ExtractDatabaseErrorMessage(dbEx);
                message.AppendLine($"🔸 {dbError}");
            }
            // معالجة أخطاء التحقق من البيانات
            else if (ex is ArgumentException || ex is InvalidOperationException)
            {
                message.AppendLine($"🔸 {ex.Message}");
            }
            // معالجة أخطاء الشبكة
            else if (ex.Message.Contains("network") || ex.Message.Contains("connection"))
            {
                message.AppendLine("🔸 مشكلة في الاتصال - تحقق من الشبكة وأعد المحاولة");
            }
            // معالجة أخطاء الملفات
            else if (ex is FileNotFoundException || ex is DirectoryNotFoundException)
            {
                message.AppendLine("🔸 ملف أو مجلد مطلوب غير موجود - تحقق من مسار الملفات");
            }
            // معالجة أخطاء الصلاحيات
            else if (ex is UnauthorizedAccessException)
            {
                message.AppendLine("🔸 ليس لديك صلاحية للوصول إلى هذا المورد");
            }
            // أخطاء عامة
            else
            {
                message.AppendLine($"🔸 خطأ غير متوقع: {ex.Message}");
            }

            message.AppendLine();
            message.AppendLine($"🆔 رقم الخطأ: {errorId}");
            message.AppendLine("💡 إذا استمر الخطأ، يرجى الاتصال بالدعم الفني مع رقم الخطأ");

            return message.ToString();
        }

        /// <summary>
        /// استخراج رسالة خطأ قاعدة البيانات
        /// </summary>
        private string ExtractDatabaseErrorMessage(DbUpdateException dbEx)
        {
            if (dbEx.InnerException == null)
                return "خطأ في قاعدة البيانات";

            var innerMessage = dbEx.InnerException.Message;

            if (innerMessage.Contains("FOREIGN KEY constraint failed"))
            {
                if (innerMessage.Contains("FieldVisitProjects.ProjectId"))
                    return "المشروع المحدد غير موجود في النظام";
                else if (innerMessage.Contains("FieldVisits.SectorId"))
                    return "القطاع المحدد غير موجود في النظام";
                else if (innerMessage.Contains("FieldVisitors.OfficerId"))
                    return "أحد الضباط المحددين غير موجود في النظام";
                else
                    return "خطأ في ربط البيانات - تحقق من صحة المعرفات";
            }
            else if (innerMessage.Contains("NOT NULL constraint failed"))
            {
                var columnMatch = System.Text.RegularExpressions.Regex.Match(innerMessage, @"NOT NULL constraint failed: (\w+)\.(\w+)");
                if (columnMatch.Success)
                {
                    var column = columnMatch.Groups[2].Value;
                    return $"الحقل '{GetArabicFieldName(column)}' مطلوب ولا يمكن أن يكون فارغاً";
                }
                return "حقل مطلوب فارغ - يجب ملء جميع الحقول المطلوبة";
            }
            else if (innerMessage.Contains("UNIQUE constraint failed"))
            {
                var columnMatch = System.Text.RegularExpressions.Regex.Match(innerMessage, @"UNIQUE constraint failed: (\w+)\.(\w+)");
                if (columnMatch.Success)
                {
                    var column = columnMatch.Groups[2].Value;
                    return $"القيمة في حقل '{GetArabicFieldName(column)}' موجودة مسبقاً - يجب استخدام قيمة مختلفة";
                }
                return "قيمة مكررة - هذه القيمة موجودة مسبقاً في النظام";
            }

            return $"خطأ في قاعدة البيانات: {innerMessage}";
        }

        /// <summary>
        /// ترجمة أسماء الحقول إلى العربية
        /// </summary>
        private string GetArabicFieldName(string fieldName)
        {
            return fieldName switch
            {
                "VisitNumber" => "رقم الزيارة",
                "MissionPurpose" => "مهمة النزول",
                "SectorId" => "القطاع",
                "ProjectNumber" => "رقم المشروع",
                "ProjectName" => "اسم المشروع",
                "OfficerId" => "الضابط",
                "Name" => "الاسم",
                "Code" => "الكود",
                _ => fieldName
            };
        }

        /// <summary>
        /// تحميل قاعدة بيانات الأخطاء
        /// </summary>
        private List<ErrorRecord> LoadErrorDatabase()
        {
            try
            {
                if (File.Exists(_errorDatabasePath))
                {
                    var json = File.ReadAllText(_errorDatabasePath, Encoding.UTF8);
                    return JsonSerializer.Deserialize<List<ErrorRecord>>(json) ?? new List<ErrorRecord>();
                }
            }
            catch
            {
                // في حالة فشل التحميل، إرجاع قائمة فارغة
            }

            return new List<ErrorRecord>();
        }

        /// <summary>
        /// الحصول على آخر الأخطاء
        /// </summary>
        public List<ErrorRecord> GetRecentErrors(int count = 50)
        {
            var errors = LoadErrorDatabase();
            return errors.Skip(Math.Max(0, errors.Count - count)).OrderByDescending(e => e.Timestamp).ToList();
        }
    }

    /// <summary>
    /// نتيجة معالجة الخطأ
    /// </summary>
    public class ErrorResult
    {
        public string ErrorId { get; set; } = string.Empty;
        public string UserMessage { get; set; } = string.Empty;
        public string TechnicalMessage { get; set; } = string.Empty;
        public string? StackTrace { get; set; }
        public DateTime Timestamp { get; set; }
        public string Context { get; set; } = string.Empty;
        public string UserAction { get; set; } = string.Empty;
    }

    /// <summary>
    /// سجل خطأ في قاعدة البيانات
    /// </summary>
    public class ErrorRecord
    {
        public string ErrorId { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string Context { get; set; } = string.Empty;
        public string UserAction { get; set; } = string.Empty;
        public string ExceptionType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? InnerExceptionMessage { get; set; }
        public string? StackTrace { get; set; }
    }
}
