using System;
using System.IO;

namespace DriverManagementSystem.Data
{
    /// <summary>
    /// إعدادات قاعدة البيانات - دعم SQL Server و SQLite
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// نوع قاعدة البيانات المستخدمة
        /// </summary>
        public enum DatabaseType
        {
            SQLite,
            SqlServer
        }

        /// <summary>
        /// نوع قاعدة البيانات الحالية
        /// </summary>
        public static DatabaseType CurrentDatabaseType { get; set; } = DatabaseType.SqlServer;

        /// <summary>
        /// الحصول على connection string لـ SQL Server
        /// </summary>
        public static string GetSqlServerConnectionString()
        {
            // يمكن تخصيص هذه القيم حسب الحاجة
            var serverName = Environment.GetEnvironmentVariable("SQL_SERVER_NAME") ?? "localhost";
            var databaseName = Environment.GetEnvironmentVariable("SQL_DATABASE_NAME") ?? "SFDSYS";
            var useWindowsAuth = Environment.GetEnvironmentVariable("SQL_USE_WINDOWS_AUTH") != "false";

            if (useWindowsAuth)
            {
                return $"Server={serverName};Database={databaseName};Trusted_Connection=true;TrustServerCertificate=true;";
            }
            else
            {
                var username = Environment.GetEnvironmentVariable("SQL_USERNAME") ?? "sa";
                var password = Environment.GetEnvironmentVariable("SQL_PASSWORD") ?? "";
                return $"Server={serverName};Database={databaseName};User Id={username};Password={password};TrustServerCertificate=true;";
            }
        }

        /// <summary>
        /// الحصول على connection string لـ SQLite
        /// </summary>
        public static string GetSqliteConnectionString()
        {
            var dataPath = Path.Combine(Directory.GetCurrentDirectory(), "Data");
            if (!Directory.Exists(dataPath))
            {
                Directory.CreateDirectory(dataPath);
            }

            var dbPath = Path.Combine(dataPath, "SFDSYS.db");
            return $"Data Source={dbPath}";
        }

        /// <summary>
        /// الحصول على connection string حسب نوع قاعدة البيانات
        /// </summary>
        public static string GetConnectionString()
        {
            return CurrentDatabaseType switch
            {
                DatabaseType.SqlServer => GetSqlServerConnectionString(),
                DatabaseType.SQLite => GetSqliteConnectionString(),
                _ => throw new NotSupportedException($"نوع قاعدة البيانات غير مدعوم: {CurrentDatabaseType}")
            };
        }
    }
}
