<Window x:Class="DriverManagementSystem.Views.DatabaseConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات قاعدة البيانات" Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إعدادات قاعدة البيانات" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"
                   Foreground="#2C3E50"/>

        <!-- محتوى الإعدادات -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- نوع قاعدة البيانات -->
                <GroupBox Header="نوع قاعدة البيانات" Margin="0,0,0,20" Padding="15">
                    <StackPanel>
                        <RadioButton x:Name="SqlServerRadio" Content="SQL Server" 
                                   FontSize="14" Margin="0,5" IsChecked="True"/>
                        <RadioButton x:Name="SqliteRadio" Content="SQLite" 
                                   FontSize="14" Margin="0,5"/>
                    </StackPanel>
                </GroupBox>

                <!-- إعدادات SQL Server -->
                <GroupBox x:Name="SqlServerGroup" Header="إعدادات SQL Server" Margin="0,0,0,20" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الخادم:" 
                                 VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox x:Name="ServerNameTextBox" Grid.Row="0" Grid.Column="1" 
                               Text="localhost" Margin="5" Padding="5"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="اسم قاعدة البيانات:" 
                                 VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox x:Name="DatabaseNameTextBox" Grid.Row="1" Grid.Column="1" 
                               Text="SFDSYS" Margin="5" Padding="5"/>

                        <CheckBox x:Name="WindowsAuthCheckBox" Grid.Row="2" Grid.ColumnSpan="2" 
                                Content="استخدام مصادقة Windows" 
                                Margin="0,10" IsChecked="True"
                                Checked="WindowsAuthCheckBox_Checked"
                                Unchecked="WindowsAuthCheckBox_Unchecked"/>

                        <TextBlock x:Name="UsernameLabel" Grid.Row="3" Grid.Column="0" Text="اسم المستخدم:" 
                                 VerticalAlignment="Center" Margin="0,5" IsEnabled="False"/>
                        <TextBox x:Name="UsernameTextBox" Grid.Row="3" Grid.Column="1" 
                               Text="sa" Margin="5" Padding="5" IsEnabled="False"/>

                        <TextBlock x:Name="PasswordLabel" Grid.Row="4" Grid.Column="0" Text="كلمة المرور:" 
                                 VerticalAlignment="Center" Margin="0,5" IsEnabled="False"/>
                        <PasswordBox x:Name="PasswordBox" Grid.Row="4" Grid.Column="1" 
                                   Margin="5" Padding="5" IsEnabled="False"/>

                        <Button x:Name="TestConnectionButton" Grid.Row="5" Grid.ColumnSpan="2"
                              Content="اختبار الاتصال" 
                              Margin="0,15,0,0" Padding="10,5"
                              Background="#3498DB" Foreground="White"
                              Click="TestConnectionButton_Click"/>
                    </Grid>
                </GroupBox>

                <!-- معلومات إضافية -->
                <GroupBox Header="معلومات" Margin="0,0,0,20" Padding="15">
                    <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="#7F8C8D">
                        • سيتم نقل جميع البيانات الموجودة من SQLite إلى SQL Server تلقائياً
                        <LineBreak/>
                        • سيتم إنشاء نسخة احتياطية من ملف SQLite الحالي
                        <LineBreak/>
                        • تأكد من أن SQL Server يعمل ويمكن الوصول إليه
                    </TextBlock>
                </GroupBox>

            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SaveButton" Content="حفظ وتطبيق" 
                    Padding="20,10" Margin="10,0"
                    Background="#27AE60" Foreground="White"
                    FontSize="14" FontWeight="Bold"
                    Click="SaveButton_Click"/>
            <Button x:Name="CancelButton" Content="إلغاء" 
                    Padding="20,10" Margin="10,0"
                    Background="#E74C3C" Foreground="White"
                    FontSize="14"
                    Click="CancelButton_Click"/>
        </StackPanel>

    </Grid>
</Window>
