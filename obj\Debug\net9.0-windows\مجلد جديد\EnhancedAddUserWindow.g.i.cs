﻿#pragma checksum "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9A337B4648C42C2B7C17A3EB16216A81923EA540"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DriverManagementSystem.Views {
    
    
    /// <summary>
    /// EnhancedAddUserWindow
    /// </summary>
    public partial class EnhancedAddUserWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 169 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FullNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FullNameError;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UsernameError;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EmailError;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox PasswordBox;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PasswordError;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PasswordStrengthPanel;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PasswordStrengthBar;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PasswordStrengthFill;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PasswordStrengthText;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox ConfirmPasswordBox;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConfirmPasswordError;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RoleComboBox;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RoleDescription;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ValidationSummary;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/%d9%85%d8%ac%d9%84%d8%af%20%d8%ac%d8%af%d9%8a%d8%af" +
                    "/enhancedadduserwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.FullNameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 171 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
            this.FullNameTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 2:
            this.FullNameError = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.UsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 181 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
            this.UsernameTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 4:
            this.UsernameError = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.EmailTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 191 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
            this.EmailTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 6:
            this.EmailError = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.PasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 214 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
            this.PasswordBox.PasswordChanged += new System.Windows.RoutedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 8:
            this.PasswordError = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.PasswordStrengthPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.PasswordStrengthBar = ((System.Windows.Controls.Border)(target));
            return;
            case 11:
            this.PasswordStrengthFill = ((System.Windows.Controls.Border)(target));
            return;
            case 12:
            this.PasswordStrengthText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ConfirmPasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 233 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
            this.ConfirmPasswordBox.PasswordChanged += new System.Windows.RoutedEventHandler(this.ValidateForm);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ConfirmPasswordError = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.RoleComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 256 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
            this.RoleComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RoleComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.RoleDescription = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.ValidationSummary = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 345 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 351 "..\..\..\..\مجلد جديد\EnhancedAddUserWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

