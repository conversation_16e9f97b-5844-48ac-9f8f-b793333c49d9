using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.Sqlite;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة نقل البيانات من SQLite إلى SQL Server
    /// </summary>
    public class DataMigrationService
    {
        /// <summary>
        /// نقل جميع البيانات من SQLite إلى SQL Server
        /// </summary>
        public async Task<bool> MigrateFromSqliteToSqlServerAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء عملية نقل البيانات من SQLite إلى SQL Server...");

                // إنشاء context للـ SQLite (المصدر) - استخدام connection string مباشر
                var sqliteConnectionString = GetSqliteConnectionString();
                var sqliteOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
                    .UseSqlite(sqliteConnectionString)
                    .Options;
                using var sqliteContext = new ApplicationDbContext(sqliteOptions);

                // التحقق من وجود بيانات في SQLite
                var hasData = await sqliteContext.Users.AnyAsync() || 
                             await sqliteContext.Drivers.AnyAsync() || 
                             await sqliteContext.FieldVisits.AnyAsync();

                if (!hasData)
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ لا توجد بيانات في SQLite للنقل");
                    return true;
                }

                // إنشاء context للـ SQL Server (الهدف)
                using var sqlServerContext = new ApplicationDbContext();

                // إنشاء قاعدة البيانات في SQL Server
                await sqlServerContext.Database.EnsureCreatedAsync();

                // نقل البيانات بالترتيب الصحيح (حسب العلاقات)
                await MigrateUsers(sqliteContext, sqlServerContext);
                await MigrateSectors(sqliteContext, sqlServerContext);
                await MigrateOfficers(sqliteContext, sqlServerContext);
                await MigrateDrivers(sqliteContext, sqlServerContext);
                await MigrateVehicles(sqliteContext, sqlServerContext);
                await MigrateProjects(sqliteContext, sqlServerContext);
                await MigrateFieldVisits(sqliteContext, sqlServerContext);
                await MigrateFieldVisitors(sqliteContext, sqlServerContext);
                await MigrateFieldVisitItineraries(sqliteContext, sqlServerContext);
                await MigrateFieldVisitProjects(sqliteContext, sqlServerContext);
                await MigrateDriverQuotes(sqliteContext, sqlServerContext);
                await MigrateProfessionalTemplates(sqliteContext, sqlServerContext);
                await MigrateContractTemplates(sqliteContext, sqlServerContext);
                await MigrateMessageDocumentations(sqliteContext, sqlServerContext);
                await MigrateMessageAttachments(sqliteContext, sqlServerContext);
                await MigrateUserPermissions(sqliteContext, sqlServerContext);

                System.Diagnostics.Debug.WriteLine("✅ تم نقل جميع البيانات بنجاح إلى SQL Server");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نقل البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex}");
                return false;
            }
        }

        private async Task MigrateUsers(ApplicationDbContext source, ApplicationDbContext target)
        {
            var users = await source.Users.ToListAsync();
            if (users.Any())
            {
                // التحقق من عدم وجود البيانات مسبقاً
                var existingCount = await target.Users.CountAsync();
                if (existingCount == 0)
                {
                    target.Users.AddRange(users);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {users.Count} مستخدم");
                }
            }
        }

        private async Task MigrateSectors(ApplicationDbContext source, ApplicationDbContext target)
        {
            var sectors = await source.Sectors.ToListAsync();
            if (sectors.Any())
            {
                var existingCount = await target.Sectors.CountAsync();
                if (existingCount == 0)
                {
                    target.Sectors.AddRange(sectors);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {sectors.Count} قطاع");
                }
            }
        }

        private async Task MigrateOfficers(ApplicationDbContext source, ApplicationDbContext target)
        {
            var officers = await source.Officers.ToListAsync();
            if (officers.Any())
            {
                var existingCount = await target.Officers.CountAsync();
                if (existingCount == 0)
                {
                    target.Officers.AddRange(officers);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {officers.Count} موظف");
                }
            }
        }

        private async Task MigrateDrivers(ApplicationDbContext source, ApplicationDbContext target)
        {
            var drivers = await source.Drivers.ToListAsync();
            if (drivers.Any())
            {
                var existingCount = await target.Drivers.CountAsync();
                if (existingCount == 0)
                {
                    target.Drivers.AddRange(drivers);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {drivers.Count} سائق");
                }
            }
        }

        private async Task MigrateVehicles(ApplicationDbContext source, ApplicationDbContext target)
        {
            var vehicles = await source.Vehicles.ToListAsync();
            if (vehicles.Any())
            {
                var existingCount = await target.Vehicles.CountAsync();
                if (existingCount == 0)
                {
                    target.Vehicles.AddRange(vehicles);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {vehicles.Count} مركبة");
                }
            }
        }

        private async Task MigrateProjects(ApplicationDbContext source, ApplicationDbContext target)
        {
            var projects = await source.Projects.ToListAsync();
            if (projects.Any())
            {
                var existingCount = await target.Projects.CountAsync();
                if (existingCount == 0)
                {
                    target.Projects.AddRange(projects);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {projects.Count} مشروع");
                }
            }
        }

        private async Task MigrateFieldVisits(ApplicationDbContext source, ApplicationDbContext target)
        {
            var fieldVisits = await source.FieldVisits.ToListAsync();
            if (fieldVisits.Any())
            {
                var existingCount = await target.FieldVisits.CountAsync();
                if (existingCount == 0)
                {
                    target.FieldVisits.AddRange(fieldVisits);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {fieldVisits.Count} زيارة ميدانية");
                }
            }
        }

        private async Task MigrateFieldVisitors(ApplicationDbContext source, ApplicationDbContext target)
        {
            var fieldVisitors = await source.FieldVisitors.ToListAsync();
            if (fieldVisitors.Any())
            {
                var existingCount = await target.FieldVisitors.CountAsync();
                if (existingCount == 0)
                {
                    target.FieldVisitors.AddRange(fieldVisitors);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {fieldVisitors.Count} زائر ميداني");
                }
            }
        }

        private async Task MigrateFieldVisitItineraries(ApplicationDbContext source, ApplicationDbContext target)
        {
            var itineraries = await source.FieldVisitItineraries.ToListAsync();
            if (itineraries.Any())
            {
                var existingCount = await target.FieldVisitItineraries.CountAsync();
                if (existingCount == 0)
                {
                    target.FieldVisitItineraries.AddRange(itineraries);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {itineraries.Count} خط سير");
                }
            }
        }

        private async Task MigrateFieldVisitProjects(ApplicationDbContext source, ApplicationDbContext target)
        {
            var fieldVisitProjects = await source.FieldVisitProjects.ToListAsync();
            if (fieldVisitProjects.Any())
            {
                var existingCount = await target.FieldVisitProjects.CountAsync();
                if (existingCount == 0)
                {
                    target.FieldVisitProjects.AddRange(fieldVisitProjects);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {fieldVisitProjects.Count} مشروع زيارة");
                }
            }
        }

        private async Task MigrateDriverQuotes(ApplicationDbContext source, ApplicationDbContext target)
        {
            var driverQuotes = await source.DriverQuotes.ToListAsync();
            if (driverQuotes.Any())
            {
                var existingCount = await target.DriverQuotes.CountAsync();
                if (existingCount == 0)
                {
                    target.DriverQuotes.AddRange(driverQuotes);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {driverQuotes.Count} عرض سائق");
                }
            }
        }

        private async Task MigrateProfessionalTemplates(ApplicationDbContext source, ApplicationDbContext target)
        {
            var templates = await source.ProfessionalTemplates.ToListAsync();
            if (templates.Any())
            {
                var existingCount = await target.ProfessionalTemplates.CountAsync();
                if (existingCount == 0)
                {
                    target.ProfessionalTemplates.AddRange(templates);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {templates.Count} قالب احترافي");
                }
            }
        }

        private async Task MigrateContractTemplates(ApplicationDbContext source, ApplicationDbContext target)
        {
            var contractTemplates = await source.ContractTemplates.ToListAsync();
            if (contractTemplates.Any())
            {
                var existingCount = await target.ContractTemplates.CountAsync();
                if (existingCount == 0)
                {
                    target.ContractTemplates.AddRange(contractTemplates);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {contractTemplates.Count} قالب عقد");
                }
            }
        }

        private async Task MigrateMessageDocumentations(ApplicationDbContext source, ApplicationDbContext target)
        {
            var messageDocumentations = await source.MessageDocumentations.ToListAsync();
            if (messageDocumentations.Any())
            {
                var existingCount = await target.MessageDocumentations.CountAsync();
                if (existingCount == 0)
                {
                    target.MessageDocumentations.AddRange(messageDocumentations);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {messageDocumentations.Count} توثيق رسالة");
                }
            }
        }

        private async Task MigrateMessageAttachments(ApplicationDbContext source, ApplicationDbContext target)
        {
            var messageAttachments = await source.MessageAttachments.ToListAsync();
            if (messageAttachments.Any())
            {
                var existingCount = await target.MessageAttachments.CountAsync();
                if (existingCount == 0)
                {
                    target.MessageAttachments.AddRange(messageAttachments);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {messageAttachments.Count} مرفق رسالة");
                }
            }
        }

        private async Task MigrateUserPermissions(ApplicationDbContext source, ApplicationDbContext target)
        {
            var userPermissions = await source.UserPermissions.ToListAsync();
            if (userPermissions.Any())
            {
                var existingCount = await target.UserPermissions.CountAsync();
                if (existingCount == 0)
                {
                    target.UserPermissions.AddRange(userPermissions);
                    await target.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم نقل {userPermissions.Count} صلاحية مستخدم");
                }
            }
        }

        /// <summary>
        /// الحصول على connection string لـ SQLite
        /// </summary>
        private string GetSqliteConnectionString()
        {
            var dataPath = Path.Combine(Directory.GetCurrentDirectory(), "Data");
            var dbPath = Path.Combine(dataPath, "SFDSYS.db");
            return $"Data Source={dbPath}";
        }
    }
}
